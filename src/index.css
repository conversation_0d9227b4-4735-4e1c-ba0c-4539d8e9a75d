@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --radius: 0.5rem;
    --background: 43 100% 96%;
    --foreground: 35 92% 33%;
    --card: 0 0% 100%;
    --card-foreground: 35 92% 33%;
    --popover: 0 0% 100%;
    --popover-foreground: 35 92% 33%;
    --primary: 35 92% 33%;
    --primary-foreground: 60 9.1% 97.8%;
    --secondary: 27 96% 61%;
    --secondary-foreground: 60 9.1% 97.8%;
    --muted: 60 4.8% 95.9%;
    --muted-foreground: 25 5.3% 44.7%;
    --accent: 60 4.8% 95.9%;
    --accent-foreground: 24 9.8% 10%;
    --border: 20 5.9% 90%;
    --input: 20 5.9% 90%;
    --ring: 35 91.0% 33%;
    font-family: "Comic Sans MS", "Chalkboard SE", "Marker Felt", sans-serif;
  }
}

/* Add a fun font for headings */
@layer components {
  h1, h2, h3 {
    @apply font-bold;
  }

  /* Page turn effect */
  .book-page-transition {
    transition: transform 0.4s ease-in-out, opacity 0.4s ease-in-out;
  }
  
  .book-page-enter {
    transform: translateX(100%);
    opacity: 0;
  }
  
  .book-page-enter-active {
    transform: translateX(0);
    opacity: 1;
  }
  
  .book-page-exit {
    transform: translateX(0);
    opacity: 1;
  }
  
  .book-page-exit-active {
    transform: translateX(-100%);
    opacity: 0;
  }
}


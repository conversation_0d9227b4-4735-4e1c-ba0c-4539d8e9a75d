import { useState, useEffect } from 'react';
import { Book } from '../types/book';

export const useBookData = () => {
  const [book, setBook] = useState<Book | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchBookData = async () => {
      try {
        const response = await fetch('/data/book_content.json');
        if (!response.ok) {
          throw new Error(`Failed to fetch book data: ${response.status}`);
        }
        const data = await response.json();
        setBook(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        console.error('Error fetching book data:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchBookData();
  }, []);
  
  return { book, loading, error };
};
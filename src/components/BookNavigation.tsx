import React from 'react';

interface BookNavigationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export const BookNavigation: React.FC<BookNavigationProps> = ({ 
  currentPage, 
  totalPages, 
  onPageChange 
}) => {
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const goToPage = (page: number) => {
    onPageChange(page);
  };

  return (
    <div className="flex flex-col items-center w-full">
      <div className="flex items-center justify-center gap-4 mb-4">
        <button
          onClick={goToPreviousPage}
          disabled={currentPage === 1}
          className={`flex items-center justify-center w-12 h-12 rounded-full ${
            currentPage === 1 
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
              : 'bg-amber-500 hover:bg-amber-600 text-white shadow-md hover:shadow-lg transition-all'
          }`}
          aria-label="Previous page"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        
        <span className="text-lg font-medium bg-white px-4 py-2 rounded-full shadow-md">
          Page {currentPage} of {totalPages}
        </span>
        
        <button
          onClick={goToNextPage}
          disabled={currentPage === totalPages}
          className={`flex items-center justify-center w-12 h-12 rounded-full ${
            currentPage === totalPages 
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
              : 'bg-amber-500 hover:bg-amber-600 text-white shadow-md hover:shadow-lg transition-all'
          }`}
          aria-label="Next page"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
      
      {/* Page thumbnails for quick navigation (visible on larger screens) */}
      <div className="hidden md:flex flex-wrap justify-center gap-2 max-w-3xl">
        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
          <button
            key={page}
            onClick={() => goToPage(page)}
            className={`w-8 h-8 flex items-center justify-center rounded-full text-sm font-medium transition-all ${
              currentPage === page
                ? 'bg-amber-500 text-white scale-110'
                : 'bg-white text-gray-700 hover:bg-amber-100'
            } shadow-sm`}
            aria-label={`Go to page ${page}`}
            aria-current={currentPage === page ? 'page' : undefined}
          >
            {page}
          </button>
        ))}
      </div>
    </div>
  );
};
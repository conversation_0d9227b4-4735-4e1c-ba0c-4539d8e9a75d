import React, { useState, useEffect } from 'react';
import { useBookData } from '../hooks/useBookData';
import { BookPage } from './BookPage';
import { BookNavigation } from './BookNavigation';
import { LoadingSpinner } from './LoadingSpinner';
import { ErrorMessage } from './ErrorMessage';

export const BookReader: React.FC = () => {
  const { book, loading, error } = useBookData();
  const [currentPageNumber, setCurrentPageNumber] = useState(1);

  // Reset to first page when book data changes
  useEffect(() => {
    if (book) {
      setCurrentPageNumber(1);
    }
  }, [book]);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPageNumber(pageNumber);
    // Scroll to top when changing pages
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Get current page data
  const currentPage = book?.pages.find(page => page.page_number === currentPageNumber);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'ArrowRight' && book && currentPageNumber < book.pages.length) {
        handlePageChange(currentPageNumber + 1);
      } else if (e.key === 'ArrowLeft' && currentPageNumber > 1) {
        handlePageChange(currentPageNumber - 1);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentPageNumber, book]);

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage message={error} />;
  if (!book || !currentPage) return <ErrorMessage message="Book content is missing or invalid" />;

  return (
    <div className="flex flex-col items-center w-full max-w-4xl mx-auto p-4 md:p-6">
      {/* Book title */}
      <h1 className="text-2xl md:text-4xl font-bold text-amber-800 mb-6 text-center">
        {book.title}
      </h1>
      
      {/* Current page */}
      <BookPage page={currentPage} />
      
      {/* Navigation */}
      <BookNavigation 
        currentPage={currentPageNumber}
        totalPages={book.pages.length}
        onPageChange={handlePageChange}
      />
    </div>
  );
};
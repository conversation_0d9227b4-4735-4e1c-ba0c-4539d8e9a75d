import React from 'react';
import { BookPage as BookPageType } from '../types/book';

interface BookPageProps {
  page: BookPageType;
}

export const BookPage: React.FC<BookPageProps> = ({ page }) => {
  return (
    <div className="flex flex-col items-center w-full h-full">
      <div className="w-full max-w-2xl overflow-hidden rounded-lg shadow-lg bg-white p-4 md:p-6 mb-4">
        <img 
          src={page.image_path} 
          alt={`Page ${page.page_number}`} 
          className="w-full h-auto object-contain rounded-md mb-4"
          loading="eager"
        />
        <p className="text-center text-lg md:text-xl font-medium text-gray-800 p-2 md:p-4">
          {page.text}
        </p>
      </div>
    </div>
  );
};